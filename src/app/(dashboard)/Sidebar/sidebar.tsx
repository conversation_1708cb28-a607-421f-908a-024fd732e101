"use client"

import Link from "next/link";
import { Open_Sans } from "next/font/google";
import { IoGridOutline, IoHome } from "react-icons/io5";
import { FaRegDotCircle } from "react-icons/fa";
import { useState } from "react";

const navigation = [
    {
        title:"dashboard",
        icon:<IoGridOutline />,
        link:"/"
    },
    {
        title:"home",
        icon:<IoHome />,
        subItems:[
            {
                title:"banner",
                link:"/dashboard/home/<USER>"
            },
            {
                title:"sponser",
                link:"/dashboard/home/<USER>"
            },
            {
                title:"dress style",
                link:"/dashboard/home/<USER>"
            },
            {
                title:"comments",
                link:"#"
            },
        ]
    },
    {
        title:"dashboard",
        icon:<IoGridOutline />,
        subItems:[
            {
                title:"alerts",
                link:"#"
            },
            {
                title:"alerts",
                link:"#"
            },
            {
                title:"alerts",
                link:"#"
            },
            {
                title:"alerts",
                link:"#"
            },
            {
                title:"alerts",
                link:"#"
            }
        ]
    },
    {
        title:"dashboard",
        icon:<IoGridOutline />,
        subItems:[
            {
                title:"alerts",
                link:"#"
            },
            {
                title:"alerts",
                link:"#"
            },
            {
                title:"alerts",
                link:"#"
            },
            {
                title:"alerts",
                link:"#"
            },
            {
                title:"alerts",
                link:"#"
            }
        ]
    }
];

const openSans = Open_Sans({
    subsets:['latin']
});
export default function Sidebar(){
    const [toogleMenu,setToggleMenu] = useState<number | null>(null);

    return(
        <>
        <div className="flex flex-col gap-y-4 py-10 px-5">
            {
                navigation.map((items,index)=>{
                    return items.subItems?
                    <div key={index} onClick={()=>{
                        if(toogleMenu === index){
                            setToggleMenu(null)
                        }else{
                            setToggleMenu(index)
                        }
                    }}>
                        <div className="flex flex-row items-center gap-x-3 py-[11px] px-2 bg-[#F6F9FF] group">
                        <span className="transition-all duration-200 ease-linear group-hover:text-blue-500">
                            {items.icon}
                        </span>
                        <span className={`${openSans.className} text-[15px] font-bold text-black capitalize transition-all duration-200 ease-linear group-hover:text-blue-500`}>
                            {items.title}
                        </span>
                        <span>
                            
                        </span>
                        </div>

                        <div className={`flex flex-col gap-y-2 pl-10 transition-all duration-500 ease-linear ${toogleMenu === index ? "max-h-[300px] overflow-y-auto customScrollbar py-2" : "max-h-0 py-0 overflow-hidden"}`}>
                            {items.subItems.map((subItems,subIndex)=>{
                                return <Link href={subItems.link} key={subIndex} className="flex flex-row items-center gap-x-2 group">
                                    <span className="transition-all duration-200 ease-linear group-hover:text-blue-500">
                                        <FaRegDotCircle />
                                    </span>
                                    <span className={`${openSans.className} text-sm font-medium capitalize transition-all duration-200 ease-linear group-hover:text-blue-500`}>{subItems.title}</span>
                                </Link>
                            })}
                        </div>
                    </div>:
                    <Link href={items.link} key={index} className="flex flex-row items-center gap-x-3 w-full bg-[#F6F9FF] py-[11px] px-2 group">
                        <span className="text-base text-black transition-all duration-200 ease-linear group-hover:text-blue-500">
                            {items.icon}
                        </span>
                        <span className={`${openSans.className} text-[15px] font-bold text-black capitalize transition-all duration-200 ease-linear group-hover:text-blue-500`}>
                            {items.title}
                        </span>
                    </Link>
                })
            }
        </div>
        </>
    )
}