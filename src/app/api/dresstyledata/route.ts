import { mkdir, writeFile, unlink } from "fs/promises";
import { NextRequest, NextResponse } from "next/server";
import path from "path";
import fs from "fs";
import { PrismaClient } from "../../../../generated/prisma";

type Obj = {
    title: string | null,
    imgpath: ImageConfig,
    colstart: string | null,
    colspan : string | null
}

type ImageConfig = {
    currentImg: string | File | null,
    previousImg: string | null
}

type IntermediateObj = {
    title?: FormDataEntryValue,
    imgpath?: FormDataEntryValue,
    colstart?: FormDataEntryValue,
    colspan?: FormDataEntryValue
}

const prisma = new PrismaClient();

export async function POST(req:NextRequest){
    try{
    const formData = await req.formData();
    // collect formData
    const collect = [...formData.entries()];

    // format the formData into normal object
    const reFormatObj = collect.reduce((acc,[key,value])=>{
        const match = key.match(/([a-zA-Z_]+)|(\d+)/g);

        if(!match) return acc;

        const [field,indexStr] = match;
        const index = parseInt(indexStr, 10);

        if(!acc[index]){
            acc[index] = {};
        }

        (acc[index] as any)[field] = value;

        return acc;
    },[] as IntermediateObj[]);

    // add to public folder and prepare for final upload to database
    const prepareObj =await Promise.all(reFormatObj.map(async({imgpath,...rest})=>{
        if(imgpath != "null"){
            const fileName = imgpath instanceof File ? imgpath.name : "";
            const separate = fileName.split(".");
            const imgName = separate[0];
            const renameImg= imgName + Date.now();
            const addFormat = renameImg + "." + separate[1];

            // buffer file
            const arrayBuffer = imgpath instanceof File ? await imgpath.arrayBuffer() : null;
            const buffer = arrayBuffer ? Buffer.from(arrayBuffer) : "";

            // place it to public folder
            const uploadDir = path.join(process.cwd(),"public/assets/");

            await mkdir(uploadDir,{recursive:true});

            await writeFile(path.join(process.cwd(),"public/assets/" + addFormat),buffer);

            return {
                title: typeof rest.title === 'string' ? rest.title : null,
                colstart: typeof rest.colstart === 'string' ? rest.colstart : null,
                colspan: typeof rest.colspan === 'string' ? rest.colspan : null,
                imgpath: addFormat
            }
        }else{
            return {
                title: typeof rest.title === 'string' ? rest.title : null,
                colstart: typeof rest.colstart === 'string' ? rest.colstart : null,
                colspan: typeof rest.colspan === 'string' ? rest.colspan : null,
                imgpath: null
            }
        }
    }));

    await prisma.dresstyle.createMany({
        data: prepareObj
    });

    }catch(error){
        return NextResponse.json(error);
    }
}
