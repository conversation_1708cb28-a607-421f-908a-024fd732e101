import { NextRequest, NextResponse } from "next/server";

type IntermediateObj = {
    id: number,
    title: string | null,
    currentImg : File | string | null,
    previousImg: string | null,
    colstart : string | null,
    colspan : string | null
}

export async function PUT(req:NextRequest){
    const formData = await req.formData();
    
    // collect the formData
    const container = [...formData.entries()];

    // convert to array of object
    const reFormatObj = container.reduce((acc,[key,value])=>{
        const match = key.match(/([a-zA-Z_]+)|(\d+)/g);

        if(!match) return acc;

        const [field,indexStr] = match;
        const index = parseInt(indexStr, 10);

        if(!acc[index]){
            acc[index] = {};
        }

        (acc[index] as any)[field] = value;

        return acc;
    },[] as IntermediateObj[]);

    // return NextResponse.json(container);
}