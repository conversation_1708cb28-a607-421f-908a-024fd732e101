"use client";

import Image from "next/image";
import { <PERSON>, <PERSON><PERSON><PERSON> } from "next/font/google";
import BannerImg from "../../../../../public/bannerImg .png";
import { useQuery } from "@tanstack/react-query";
import axios from "axios";
import ClientLoading from "../../loading";

type BannerData={
    title:string|null,
    banner_text:string|null,
    imgpath:string
}

const anton = <PERSON>({
    subsets:['latin'],
    weight:'400'
});

const manrope = Manrope({
    subsets:['latin']
});
export default function Banner(){
    const {isPending,isError,data} = useQuery<BannerData>({
        queryKey:["clientBannerData"],
        queryFn:async ()=>{
            const response = await axios("api/bannerretrievedata");
            const result = await response.data?.data[0];

            return result;
        }
    });
    return(
        <>
        <section className="mt-[140px] bg-[#F2F0F1]">
            {
                isPending?
                <div className="h-full w-full flex justify-center items-center py-[120px]">
                    <ClientLoading/>
                </div>:
                isError?
                <div></div>:
                <div className="max-w-screen-2xl px-12 xl:pt-0 pt-10 grid xl:grid-cols-3 grid-cols-1 items-center">
                <div className="xl:pl-[52px] col-span-2">
                    <div className="xl:w-[577px] w-full mb-8">
                        <h1 className={`${anton.className} font-bold xl:text-[64px] text-4xl text-black uppercase xl:leading-[64px] leading-[34px]`}>
                            {/* find clothes that matches your style */}
                            {data.title}
                        </h1>
                    </div>

                    <div className="xl:w-[545px] w-full mb-8">
                        <p className={`${manrope.className} font-normal xl:text-base text-sm leading-[22px] text-[#000000]/60`}>
                            {/* Browse through our diverse range of meticulously crafted graments, designed to bring out your individuality and cater to your sense of style */}
                            {data.banner_text}
                        </p>
                    </div>

                    <div className="mb-12">
                        <button className={`${manrope.className} text-white capitalize font-medium py-[15px] px-[67.5px] bg-black rounded-full`}>
                            shop now
                        </button>
                    </div>

                    <div className="grid xl:grid-cols-3 grid-cols-2 gap-x-8 gap-y-8">
                        <div className=" border border-[#000000]/10 border-t-0 border-b-0 border-l-0">
                            <h4 className={`${manrope.className} font-bold xl:text-[40px] text-2xl text-[#000000]`}>
                                200+
                            </h4>
                            <p className={`${manrope.className} xl:text-base text-xs font-normal leading-[22px] text-[#000000]/60 capitalize`}>
                                international brands
                            </p>
                        </div>
                        <div className="xl:border border-0 xl:border-[#000000]/10 xl:border-t-0 xl:border-b-0 xl:border-l-0">
                            <h4 className={`${manrope.className} font-bold xl:text-[40px] text-2xl text-[#000000]`}>
                                2,000+
                            </h4>
                            <p className={`${manrope.className} xl:text-base text-xs font-normal leading-[22px] text-[#000000]/60 capitalize`}>
                                high-quality products
                            </p>
                        </div>
                        <div className="xl:col-span-1 col-span-2 flex flex-col xl:items-start items-center">
                            <h4 className={`${manrope.className} font-bold xl:text-[40px] text-2xl text-[#000000]`}>
                                30000+
                            </h4>
                            <p className={`${manrope.className} xl:text-base text-xs font-normal leading-[22px] text-[#000000]/60 capitalize`}>
                                happy customers
                            </p>
                        </div>
                    </div>
                </div>

                <div className="pt-10 flex justify-end">
                    <div className="xl:h-[650px] h-full xl:w-[450px] w-full">
                        <Image src={data?.imgpath ?? ""} alt="bannerImg" height={650} width={450} className="h-full w-full"/>
                    </div>
                </div>
            </div>
            }
            
        </section>
        </>
    )
}