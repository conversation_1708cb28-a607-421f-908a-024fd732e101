import Image from "next/image";
import { <PERSON><PERSON><PERSON>, <PERSON> } from "next/font/google";
import Casual from "../../../../../public/casual.png";
import Formal from "../../../../../public/formal.png";
import Party from "../../../../../public/party.png";
import Gym from "../../../../../public/gym.png";

const manrope = Manrope({
    subsets:['latin'],
});

const anton = Anton({
    subsets:['latin'],
    weight:'400'
});

export default function BrowseDress(){
    return(
        <>
        <section>
            <div className="max-w-screen-2xl mx-auto xl:px-[148px] px-5">
                <div className="w-full bg-[#F0F0F0] xl:px-16 px-5 py-[70px] rounded-lg">
                    <div className="w-full text-center mb-16">
                        <h3 className={`${anton.className} font-bold text-5xl uppercase text-black`}>
                            browse by dress style
                        </h3>
                    </div>

                    <div className="w-full grid xl:grid-cols-3 grid-cols-1 xl:gap-x-5 gap-y-5 mb-5">
                        <div className="h-[289px] w-full rounded-lg bg-white relative overflow-hidden">
                                <Image src={Casual} alt="productImg" className="absolute top-0 right-0 h-full w-full object-cover"/>

                            <span className={`${manrope.className} font-bold text-4xl capitalize text-black absolute top-[25px] left-9`}>
                                Casusal
                            </span>
                        </div>

                        <div className="xl:col-span-2 h-[289px] rounded-lg bg-white relative overflow-hidden">
                            <Image src={Formal} alt="productImg" className="absolute top-0 right-0 h-full w-full object-cover"/>

                            <span className={`${manrope.className} font-bold text-4xl capitalize text-black absolute top-[25px] left-9`}>
                                Formal
                            </span>
                        </div>
                    </div>

                    <div className="w-full grid xl:grid-cols-3 grid-cols-1 xl:gap-x-5 gap-y-5">
                        <div className="xl:col-span-2 h-[289px] rounded-lg bg-white relative overflow-hidden">
                            <Image src={Party} alt="productImg" className="absolute top-0 right-0 h-full w-full object-cover"/>

                            <span className={`${manrope.className} font-bold text-4xl capitalize text-black absolute top-[25px] left-9`}>
                                Party
                            </span>
                        </div>

                        <div className="h-[289px] w-full rounded-lg bg-white relative overflow-hidden">
                                <Image src={Gym} alt="productImg" className="absolute top-0 right-0 h-full w-full object-cover"/>

                            <span className={`${manrope.className} font-bold text-4xl capitalize text-black absolute top-[25px] left-9`}>
                                Gym
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        </>
    )
}