import { <PERSON>, <PERSON><PERSON><PERSON> } from "next/font/google";
import Link from "next/link";
import { FiSearch } from "react-icons/fi";
import { IoCartOutline } from "react-icons/io5";
import { LuCircleUser } from "react-icons/lu";

const anton = <PERSON>({
    subsets: ['latin'],
    weight: "400"
});

const manrope = Manrope({
    subsets:['latin']
});

export default function Navbar(){
    return(
        <>
        <nav className="w-full h-20 lg:grid hidden grid-cols-10 xl:gap-x-10 lg:gap-x-4 items-center xl:px-[100px] lg:px-10">
            <div>
                <Link href="/" className={`${anton.className} font-bold xl:text-[32px] lg:text-2xl uppercase`}>
                    shop.co
                </Link>
            </div>

            <div className={`col-span-3 flex flex-row xl:justify-between lg:justify-center lg:gap-x-3 font-normal text-base ${manrope.className} capitalize`}>
                <span>
                    shop
                </span>
                <Link href="/pages/onsale">
                    on sale
                </Link>
                <Link href="#">
                    new arrivals
                </Link>
                <Link href="#">
                    brands
                </Link>
            </div>

            <div className="col-span-5 relative w-full h-12 bg-[#F0F0F0] rounded-full">
                <div className="absolute h-full w-full top-0 left-0">
                    <input type="text" className={`h-full w-full pl-[52px] ${manrope.className} font-normal text-base focus:outline-none focus:ring-0 focus:border-none`} placeholder="search for products"/>
                </div>
                <span className="absolute text-[#b2bec3] text-xl top-[13.86px] left-[17.86px]">
                    <FiSearch />
                </span>
            </div>

            <div className="flex flex-row w-full justify-between">
                <span className="text-2xl">
                    <IoCartOutline />
                </span>

                <span className="text-2xl">
                    <LuCircleUser />
                </span>
            </div>
        </nav>
        </>
    )
}